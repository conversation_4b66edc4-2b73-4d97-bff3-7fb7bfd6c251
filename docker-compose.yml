services:
  openindoor6:
    # image: openindoor6
    build: .
    working_dir: /root/openindoor6
    command: |
      yarn run start
      
    volumes:
    - ./src:/root/openindoor6/src
    - ./dist:/root/openindoor6/dist
    - ./webpack.config.js:/root/openindoor6/webpack.config.js
    - ./package.json:/root/openindoor6/package.json
    - ./public:/root/openindoor6/public
    # - ./.babel.rc:/root/openindoor6/.babel.rc
    ports:
    - 3020:3020
